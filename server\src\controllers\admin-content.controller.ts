import { Request, Response, NextFunction } from "express";
import { PrismaClient } from "@prisma/client";
import { createError } from "../utils/wrapController";
import { deleteFile, getFileUrl } from "../middleware/upload";

const prisma = new PrismaClient();

// Request interfaces
export interface CreateCarouselSlideRequest {
  title: string;
  subtitle?: string;
  image: string;
  link?: string;
  isActive: boolean;
  sortOrder: number;
}

export interface UpdateCompanySettingsRequest {
  companyName?: string;
  companyEmail?: string;
  companyPhone?: string;
  companyAddress1?: string;
  companyAddress2?: string;
  companyCity?: string;
  companyState?: string;
  companyZip?: string;
  companyCountry?: string;
  metaTitle?: string;
  metaDescription?: string;
  copyrightText?: string;
  newsletterTitle?: string;
  newsletterDescription?: string;
}

export interface HomepageDesignRequest {
  navbarDesign?: any;
  heroDesign?: any;
  cardsDesign?: any;
  typographyDesign?: any;
  pageDesign?: any;
  buttonDesign?: any;
}

/**
 * GET /api/admin/content/carousel
 * Get all carousel slides
 */
export const getCarouselSlides = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const slides = await prisma.carouselSlide.findMany({
      orderBy: { sortOrder: "asc" },
    });

    res.json({ slides });
  } catch (error) {
    console.error("Get carousel slides error:", error);
    next(createError(500, "Failed to get carousel slides"));
  }
};

/**
 * POST /api/admin/content/carousel
 * Create new carousel slide
 */
export const createCarouselSlide = async (
  req: Request<{}, {}, CreateCarouselSlideRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { title, subtitle, image, link, isActive, sortOrder } = req.body;

    const slide = await prisma.carouselSlide.create({
      data: {
        title,
        subtitle,
        image,
        link,
        isActive,
        sortOrder,
      },
    });

    res.status(201).json({
      message: "Carousel slide created successfully",
      slide,
    });
  } catch (error) {
    console.error("Create carousel slide error:", error);
    next(createError(500, "Failed to create carousel slide"));
  }
};

/**
 * GET /api/admin/content/company-settings
 * Get company settings
 */
export const getCompanySettings = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst();

    // Convert relative logoUrl to full URL
    if (settings && settings.logoUrl) {
      settings.logoUrl = getFileUrl(settings.logoUrl);
    }

    res.json({ settings });
  } catch (error) {
    console.error("Get company settings error:", error);
    next(createError(500, "Failed to get company settings"));
  }
};

/**
 * PUT /api/admin/content/company-settings
 * Update company settings
 */
export const updateCompanySettings = async (
  req: Request<{}, {}, UpdateCompanySettingsRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const updateData = req.body;

    // Get existing settings or create new ones
    let settings = await prisma.companySettings.findFirst();

    if (settings) {
      settings = await prisma.companySettings.update({
        where: { id: settings.id },
        data: updateData,
      });
    } else {
      settings = await prisma.companySettings.create({
        data: {
          id: "default",
          companyName: updateData.companyName || "Multi Store",
          companyEmail: updateData.companyEmail || "<EMAIL>",
          ...updateData,
        },
      });
    }

    // Convert relative logoUrl to full URL
    if (settings.logoUrl) {
      settings.logoUrl = getFileUrl(settings.logoUrl);
    }

    res.json({
      message: "Company settings updated successfully",
      settings,
    });
  } catch (error) {
    console.error("Update company settings error:", error);
    next(createError(500, "Failed to update company settings"));
  }
};

/**
 * POST /api/admin/content/upload-logo
 * Upload company logo
 */
export const uploadLogo = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.file) {
      next(createError(400, "No file uploaded"));
      return;
    }

    const logoPath = `logos/${req.file.filename}`;

    // Get existing settings
    let settings = await prisma.companySettings.findFirst();

    // Delete old logo if exists
    if (settings?.logoUrl) {
      deleteFile(settings.logoUrl);
    }

    // Update or create settings with new logo
    if (settings) {
      settings = await prisma.companySettings.update({
        where: { id: settings.id },
        data: { logoUrl: logoPath },
      });
    } else {
      settings = await prisma.companySettings.create({
        data: {
          id: "default",
          companyName: "Multi Store",
          companyEmail: "<EMAIL>",
          logoUrl: logoPath,
        },
      });
    }

    // Convert to full URL
    settings.logoUrl = getFileUrl(settings.logoUrl);

    res.json({
      message: "Logo uploaded successfully",
      settings,
    });
  } catch (error) {
    console.error("Upload logo error:", error);
    next(createError(500, "Failed to upload logo"));
  }
};

/**
 * DELETE /api/admin/content/remove-logo
 * Remove company logo
 */
export const removeLogo = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst();

    if (!settings || !settings.logoUrl) {
      next(createError(404, "No logo found"));
      return;
    }

    // Delete file
    deleteFile(settings.logoUrl);

    // Update settings
    const updatedSettings = await prisma.companySettings.update({
      where: { id: settings.id },
      data: { logoUrl: null },
    });

    res.json({
      message: "Logo removed successfully",
      settings: updatedSettings,
    });
  } catch (error) {
    console.error("Remove logo error:", error);
    next(createError(500, "Failed to remove logo"));
  }
};

/**
 * POST /api/admin/content/upload-carousel-image
 * Upload carousel image
 */
export const uploadCarouselImage = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    console.log("Carousel upload - req.file:", req.file);

    if (!req.file) {
      next(createError(400, "No file uploaded"));
      return;
    }

    const imagePath = `carousel/${req.file.filename}`;
    const imageUrl = getFileUrl(imagePath);

    console.log("Carousel upload - imagePath:", imagePath);
    console.log("Carousel upload - imageUrl:", imageUrl);

    const response = {
      success: true,
      message: "Carousel image uploaded successfully",
      imageUrl,
    };

    console.log("Carousel upload - response:", response);

    res.json(response);
  } catch (error) {
    console.error("Upload carousel image error:", error);

    // Clean up uploaded file if database operation failed
    if (req.file) {
      deleteFile(`carousel/${req.file.filename}`);
    }

    next(createError(500, "Failed to upload carousel image"));
  }
};

/**
 * GET /api/admin/content/homepage-design
 * Get homepage design settings
 */
export const getHomepageDesign = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const settings = await prisma.companySettings.findFirst({
      select: {
        navbarDesign: true,
        heroDesign: true,
        cardsDesign: true,
        typographyDesign: true,
        pageDesign: true,
        buttonDesign: true,
      },
    });

    if (!settings) {
      // Return default settings if none exist
      res.json({
        settings: {
          navbarDesign: getDefaultNavbarDesign(),
          heroDesign: getDefaultHeroDesign(),
          cardsDesign: getDefaultCardsDesign(),
          typographyDesign: getDefaultTypographyDesign(),
          pageDesign: getDefaultPageDesign(),
          buttonDesign: getDefaultButtonDesign(),
        },
      });
      return;
    }

    res.json({ settings });
  } catch (error) {
    console.error("Fetch homepage design error:", error);
    next(createError(500, "Failed to fetch homepage design settings"));
  }
};

/**
 * PUT /api/admin/content/homepage-design
 * Update homepage design settings
 */
export const updateHomepageDesign = async (
  req: Request<{}, {}, HomepageDesignRequest>,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      navbarDesign,
      heroDesign,
      cardsDesign,
      typographyDesign,
      pageDesign,
      buttonDesign,
    } = req.body;

    // Get or create company settings
    let settings = await prisma.companySettings.findFirst();

    if (!settings) {
      settings = await prisma.companySettings.create({
        data: {
          id: "default",
          companyName: "Multi Store",
          companyEmail: "<EMAIL>",
          navbarDesign,
          heroDesign,
          cardsDesign,
          typographyDesign,
          pageDesign,
          buttonDesign,
        },
      });
    } else {
      settings = await prisma.companySettings.update({
        where: { id: settings.id },
        data: {
          navbarDesign,
          heroDesign,
          cardsDesign,
          typographyDesign,
          pageDesign,
          buttonDesign,
          updatedAt: new Date(),
        },
      });
    }

    res.json({
      success: true,
      message: "Homepage design settings updated successfully",
      settings,
    });
  } catch (error) {
    console.error("Update homepage design error:", error);
    next(createError(500, "Failed to update homepage design settings"));
  }
};

// Default design configurations
function getDefaultNavbarDesign() {
  return {
    width: "full",
    alignment: "center",
    height: "default",
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    itemSpacing: "normal",
    linkStyle: "default",
    hoverStyle: "default",
    activeStyle: "default",
    textStyles: {
      fontFamily: "Inter",
      fontSize: 16,
      fontWeight: "500",
      color: "#000000",
    },
  };
}

function getDefaultHeroDesign() {
  return {
    width: "container",
    height: "60vh",
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 30, right: 30, bottom: 0, left: 30 },
    background: {
      type: "gradient",
      color: "#ffffff",
      gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-r" },
    },
    slides: [
      {
        id: "default-1",
        title: "Slide 1",
        subtitle:
          "Here you should load the picture of the slide or add the background color and then edit the titles, subtitle and the CTA button",
        image: null,
        link: "",
      },
      {
        id: "default-2",
        title: "Slide 2",
        subtitle:
          "Here you should load the picture of the slide or add the background color and then edit the titles, subtitle and the CTA button",
        image: null,
        link: "",
      },
    ],
    imagePosition: "cover",
  };
}

function getDefaultCardsDesign() {
  return {
    borderRadius: "rounded-lg",
    shadow: "shadow-xl",
    backgroundColor: "#ffffff",
    padding: "p-6",
  };
}

function getDefaultTypographyDesign() {
  return {
    // DaisyUI default typography - no custom styles applied
    h1: {
      fontFamily: "inherit", // Uses DaisyUI default
      fontSize: "inherit", // Uses DaisyUI default
      fontWeight: "inherit", // Uses DaisyUI default
      color: "inherit", // Uses DaisyUI default
    },
    h2: {
      fontFamily: "inherit",
      fontSize: "inherit",
      fontWeight: "inherit",
      color: "inherit",
    },
    h3: {
      fontFamily: "inherit",
      fontSize: "inherit",
      fontWeight: "inherit",
      color: "inherit",
    },
    h4: {
      fontFamily: "inherit",
      fontSize: "inherit",
      fontWeight: "inherit",
      color: "inherit",
    },
  };
}

function getDefaultPageDesign() {
  return {
    backgroundColor: "inherit", // Uses DaisyUI default
    backgroundType: "color",
    gradient: { from: "#ffffff", to: "#f3f4f6", direction: "to-b" },
    backgroundImage: null,
    overlay: { type: "none", color: "#000000", opacity: 0.5 },
  };
}

function getDefaultButtonDesign() {
  return {
    primary: {
      style: "default", // Uses DaisyUI default
      size: "default",
      colors: { bg: "inherit", text: "inherit", border: "inherit" }, // Uses DaisyUI defaults
    },
    secondary: {
      style: "default",
      size: "default",
      colors: { bg: "inherit", text: "inherit", border: "inherit" },
    },
  };
}
