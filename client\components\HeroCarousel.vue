<template>
  <!-- Carousel Container with margins and rounded corners -->
  <div :class="containerClasses" :style="containerStyles">
    <div :class="carouselClasses" :style="carouselStyles">
      <div
        v-for="(slide, index) in displaySlides"
        :key="slide.id"
        :id="`slide${index + 1}`"
        class="carousel-item relative w-full"
      >
        <div
          v-if="slide.image && !slide.image.startsWith('blob:')"
          class="w-full h-full rounded-box"
          :style="{
            backgroundImage: `url(${slide.image})`,
            backgroundSize: heroDesign?.imagePosition || 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }"
        ></div>
        <div
          v-else
          class="w-full h-full rounded-box flex items-center justify-center"
          :style="carouselBackgroundStyle"
        >
          <div class="text-center text-base-content opacity-50">
            <Icon name="heroicons:photo" class="h-16 w-16 mx-auto mb-2" />
            <p>Upload an image for this slide</p>
          </div>
        </div>

        <!-- Overlay content -->
        <div
          class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center rounded-box"
        >
          <div class="text-center text-white max-w-2xl px-4">
            <h1 v-if="slide.title" class="text-4xl md:text-6xl font-bold mb-4">
              {{ slide.title }}
            </h1>
            <p v-if="slide.subtitle" class="text-lg md:text-xl mb-8">
              {{ slide.subtitle }}
            </p>
            <NuxtLink
              v-if="slide.link"
              :to="slide.link"
              class="btn btn-primary btn-lg"
            >
              Shop Now
            </NuxtLink>
          </div>
        </div>

        <!-- Navigation arrows - positioned at top right -->
        <div class="absolute top-4 right-4 flex gap-2">
          <button
            @click="goToSlide(index === 0 ? displaySlides.length : index)"
            class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
            title="Previous slide"
          >
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </button>
          <button
            @click="
              goToSlide(index === displaySlides.length - 1 ? 1 : index + 2)
            "
            class="btn btn-circle btn-sm btn-ghost bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 backdrop-blur-sm"
            title="Next slide"
          >
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Indicator dots -->
  <!-- <div
    v-if="displaySlides.length > 1"
    class="flex w-full justify-center gap-2 py-2"
  >
    <a
      v-for="(slide, index) in displaySlides"
      :key="`indicator-${slide.id}`"
      :href="`#slide${index + 1}`"
      class="btn btn-xs"
      :class="{ 'btn-primary': index === 0, 'btn-outline': index !== 0 }"
    >
      {{ index + 1 }}
    </a>
  </div> -->
</template>

<script setup>
const contentStore = useContentStore();

// Get carousel slides from store
const storeSlides = computed(() => contentStore.activeCarouselSlides);

// Get homepage design settings
const homepageDesign = computed(() => contentStore.homepageDesign);
const heroDesign = computed(() => homepageDesign.value?.heroDesign);

// Default slides if none are loaded
const defaultSlides = [
  {
    id: "default-1",
    title: "Slide 1",
    subtitle:
      "Here you should load the picture of the slide or add the background color and then edit the titles, subtitle and the CTA button",
    image: null,
    link: "",
  },
  {
    id: "default-2",
    title: "Slide 2",
    subtitle:
      "Here you should load the picture of the slide or add the background color and then edit the titles, subtitle and the CTA button",
    image: null,
    link: "",
  },
];

// Use default slides if no slides are available
const displaySlides = computed(() => {
  return storeSlides.value.length > 0 ? storeSlides.value : defaultSlides;
});

// Navigation method that prevents scrolling
const goToSlide = (slideNumber) => {
  const targetSlide = document.getElementById(`slide${slideNumber}`);
  if (targetSlide) {
    targetSlide.scrollIntoView({
      behavior: "smooth",
      block: "nearest",
      inline: "start",
    });
  }
};

// Hero styling from design settings
const containerClasses = computed(() => {
  if (!heroDesign.value) return "mx-4 md:mx-8 mt-6 mb-8";

  const classes = [];

  // Width classes - always center the carousel
  switch (heroDesign.value.width) {
    case "container":
      classes.push("container mx-auto px-4");
      break;
    case "lg":
      classes.push("max-w-5xl mx-auto px-4");
      break;
    case "md":
      classes.push("max-w-3xl mx-auto px-4");
      break;
    case "sm":
      classes.push("max-w-2xl mx-auto px-4");
      break;
    default:
      classes.push("w-full mx-auto px-4");
      break;
  }

  classes.push("mt-6 mb-8"); // Default spacing

  return classes.join(" ");
});

const containerStyles = computed(() => {
  if (!heroDesign.value) return {};

  const styles = {};

  // Margin
  if (heroDesign.value.margin) {
    const m = heroDesign.value.margin;
    styles.marginTop = `${m.top || 0}px`;
    styles.marginRight = `${m.right || 0}px`;
    styles.marginBottom = `${m.bottom || 0}px`;
    styles.marginLeft = `${m.left || 0}px`;
  }

  return styles;
});

const carouselClasses = computed(() => {
  return "carousel carousel-center rounded-box w-full shadow-lg";
});

const carouselStyles = computed(() => {
  if (!heroDesign.value) return { height: "500px" };

  const styles = {
    height: heroDesign.value.height || "500px",
  };

  // Padding
  if (heroDesign.value.padding) {
    const p = heroDesign.value.padding;
    styles.paddingTop = `${p.top || 0}px`;
    styles.paddingRight = `${p.right || 0}px`;
    styles.paddingBottom = `${p.bottom || 0}px`;
    styles.paddingLeft = `${p.left || 0}px`;
  }

  // Background
  if (heroDesign.value.background) {
    const bg = heroDesign.value.background;
    if (bg.type === "gradient") {
      const direction = bg.gradient.direction.replace("to-", "");
      styles.background = `linear-gradient(${direction}, ${bg.gradient.from}, ${bg.gradient.to})`;
    } else {
      styles.backgroundColor = bg.color;
    }
  }

  return styles;
});

// Background style for slides without images
const carouselBackgroundStyle = computed(() => {
  if (!heroDesign.value?.background) return { backgroundColor: "#f3f4f6" };

  const bg = heroDesign.value.background;
  if (bg.type === "gradient") {
    const direction = bg.gradient.direction.replace("to-", "");
    return {
      background: `linear-gradient(${direction}, ${bg.gradient.from}, ${bg.gradient.to})`,
    };
  } else {
    return {
      backgroundColor: bg.color || "#f3f4f6",
    };
  }
});
</script>
