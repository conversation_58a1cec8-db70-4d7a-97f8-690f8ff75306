import express, { Router, Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import {
  getCarouselSlides,
  createCarouselSlide,
  getCompanySettings,
  updateCompanySettings,
  uploadLogo,
  removeLogo,
  uploadCarouselImage as uploadCarouselImageController,
  getHomepageDesign,
  updateHomepageDesign,
} from "../controllers/admin-content.controller";
import { authenticate, requireAdmin } from "../middleware/auth";
import {
  uploadLogo as uploadLogoMiddleware,
  uploadCarouselImage,
  handleUploadError,
  getFileUrl,
  deleteFile,
} from "../middleware/upload";
import { wrapController } from "../utils/wrapController";

const prisma = new PrismaClient();

const router: Router = express.Router();

// Apply authentication and admin requirement to all routes
router.use(authenticate);
router.use(requireAdmin);

// Carousel management routes
router.get("/carousel", wrapController(getCarouselSlides));
router.post("/carousel", wrapController(createCarouselSlide));

// Company settings routes
router.get("/company-settings", wrapController(getCompanySettings));
router.put("/company-settings", wrapController(updateCompanySettings));

// Logo management routes
router.post(
  "/upload-logo",
  uploadLogoMiddleware,
  handleUploadError,
  wrapController(uploadLogo)
);
router.delete("/remove-logo", wrapController(removeLogo));

// Carousel image upload route
router.post(
  "/upload-carousel-image",
  uploadCarouselImage,
  handleUploadError,
  wrapController(uploadCarouselImageController)
);

// Homepage design routes
router.get("/homepage-design", wrapController(getHomepageDesign));
router.put("/homepage-design", wrapController(updateHomepageDesign));

// Legacy routes (to be refactored later)
router.get("/carousel-legacy", async (req, res) => {
  try {
    const slides = await prisma.carouselSlide.findMany({
      orderBy: { sortOrder: "asc" },
    });

    res.json({ slides });
  } catch (error) {
    console.error("Get carousel slides error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to get carousel slides",
    });
  }
});

/**
 * POST /api/admin/content/carousel
 * Create new carousel slide
 */
router.post("/carousel", async (req, res) => {
  try {
    const { title, subtitle, image, link, isActive, sortOrder } = req.body;

    const slide = await prisma.carouselSlide.create({
      data: {
        title,
        subtitle,
        image,
        link,
        isActive,
        sortOrder,
      },
    });

    res.status(201).json({
      message: "Carousel slide created successfully",
      slide,
    });
  } catch (error) {
    console.error("Create carousel slide error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create carousel slide",
    });
  }
});

/**
 * PUT /api/admin/content/carousel/:id
 * Update carousel slide
 */
router.put("/carousel/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const slide = await prisma.carouselSlide.update({
      where: { id },
      data: updateData,
    });

    res.json({
      message: "Carousel slide updated successfully",
      slide,
    });
  } catch (error) {
    console.error("Update carousel slide error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to update carousel slide",
    });
  }
});

/**
 * DELETE /api/admin/content/carousel/:id
 * Delete carousel slide
 */
router.delete("/carousel/:id", async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.carouselSlide.delete({
      where: { id },
    });

    res.json({
      message: "Carousel slide deleted successfully",
    });
  } catch (error) {
    console.error("Delete carousel slide error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to delete carousel slide",
    });
  }
});

// ===== BENEFIT CARDS MANAGEMENT =====

/**
 * GET /api/admin/content/benefits
 * Get all benefit cards
 */
router.get("/benefits", async (req, res) => {
  try {
    const benefits = await prisma.benefitCard.findMany({
      orderBy: { sortOrder: "asc" },
    });

    res.json({ benefits });
  } catch (error) {
    console.error("Get benefit cards error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to get benefit cards",
    });
  }
});

/**
 * POST /api/admin/content/benefits
 * Create new benefit card
 */
router.post("/benefits", async (req, res) => {
  try {
    const { title, description, icon, image, isActive, sortOrder } = req.body;

    const benefit = await prisma.benefitCard.create({
      data: {
        title,
        description,
        icon,
        image,
        isActive,
        sortOrder,
      },
    });

    res.status(201).json({
      message: "Benefit card created successfully",
      benefit,
    });
  } catch (error) {
    console.error("Create benefit card error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create benefit card",
    });
  }
});

/**
 * PUT /api/admin/content/benefits/:id
 * Update benefit card
 */
router.put("/benefits/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const benefit = await prisma.benefitCard.update({
      where: { id },
      data: updateData,
    });

    res.json({
      message: "Benefit card updated successfully",
      benefit,
    });
  } catch (error) {
    console.error("Update benefit card error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to update benefit card",
    });
  }
});

/**
 * DELETE /api/admin/content/benefits/:id
 * Delete benefit card
 */
router.delete("/benefits/:id", async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.benefitCard.delete({
      where: { id },
    });

    res.json({
      message: "Benefit card deleted successfully",
    });
  } catch (error) {
    console.error("Delete benefit card error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to delete benefit card",
    });
  }
});

// ===== LIMITED TIME OFFER MANAGEMENT =====

/**
 * GET /api/admin/content/limited-time-offer
 * Get limited time offer
 */
router.get("/limited-time-offer", async (req, res) => {
  try {
    const offer = await prisma.limitedTimeOffer.findFirst({
      orderBy: { createdAt: "desc" },
    });

    res.json({ offer });
  } catch (error) {
    console.error("Get limited time offer error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to get limited time offer",
    });
  }
});

/**
 * POST /api/admin/content/limited-time-offer
 * Create/Update limited time offer
 */
router.post("/limited-time-offer", async (req, res) => {
  try {
    const {
      badge,
      title,
      subtitle,
      buttonText,
      buttonLink,
      endDate,
      isActive,
    } = req.body;

    // Deactivate existing offers
    await prisma.limitedTimeOffer.updateMany({
      data: { isActive: false },
    });

    const offer = await prisma.limitedTimeOffer.create({
      data: {
        badge,
        title,
        subtitle,
        buttonText,
        buttonLink,
        endDate: endDate ? new Date(endDate) : null,
        isActive,
      },
    });

    res.status(201).json({
      message: "Limited time offer created successfully",
      offer,
    });
  } catch (error) {
    console.error("Create limited time offer error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create limited time offer",
    });
  }
});

// ===== COMPANY SETTINGS MANAGEMENT =====

/**
 * GET /api/admin/content/company-settings
 * Get company settings
 */
router.get("/company-settings", async (req, res) => {
  try {
    const settings = await prisma.companySettings.findFirst();

    if (!settings) {
      return res.status(404).json({
        error: true,
        message: "Company settings not found",
      });
    }

    // Add full URL for logo if it exists
    if (settings.logoUrl) {
      settings.logoUrl = getFileUrl(settings.logoUrl);
    }

    res.json({
      message: "Company settings retrieved successfully",
      settings,
    });
  } catch (error) {
    console.error("Error fetching company settings:", error);
    res.status(500).json({
      error: true,
      message: "Failed to fetch company settings",
    });
  }
});

/**
 * PUT /api/admin/content/company-settings
 * Update company settings
 */
router.put("/company-settings", async (req, res) => {
  try {
    const {
      companyName,
      companyEmail,
      companyPhone,
      companyAddress1,
      companyAddress2,
      companyCity,
      companyState,
      companyZip,
      companyCountry,
      facebookUrl,
      twitterUrl,
      instagramUrl,
      linkedinUrl,
      metaTitle,
      metaDescription,
      newsletterTitle,
      newsletterDescription,
      copyrightText,
    } = req.body;

    // Check if settings exist
    let settings = await prisma.companySettings.findFirst();

    if (settings) {
      // Update existing settings
      settings = await prisma.companySettings.update({
        where: { id: settings.id },
        data: {
          companyName,
          companyEmail,
          companyPhone,
          companyAddress1,
          companyAddress2,
          companyCity,
          companyState,
          companyZip,
          companyCountry,
          facebookUrl,
          twitterUrl,
          instagramUrl,
          linkedinUrl,
          metaTitle,
          metaDescription,
          newsletterTitle,
          newsletterDescription,
          copyrightText,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new settings
      settings = await prisma.companySettings.create({
        data: {
          companyName,
          companyEmail,
          companyPhone,
          companyAddress1,
          companyAddress2,
          companyCity,
          companyState,
          companyZip,
          companyCountry,
          facebookUrl,
          twitterUrl,
          instagramUrl,
          linkedinUrl,
          metaTitle,
          metaDescription,
          newsletterTitle,
          newsletterDescription,
          copyrightText,
        },
      });
    }

    // Add full URL for logo if it exists
    if (settings.logoUrl) {
      settings.logoUrl = getFileUrl(settings.logoUrl);
    }

    res.json({
      message: "Company settings updated successfully",
      settings,
    });
  } catch (error) {
    console.error("Error updating company settings:", error);
    res.status(500).json({
      error: true,
      message: "Failed to update company settings",
    });
  }
});

/**
 * POST /api/admin/content/upload-logo
 * Upload company logo
 */
router.post(
  "/upload-logo",
  uploadLogoMiddleware,
  handleUploadError,
  async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          error: true,
          message: "No file uploaded",
        });
      }

      const logoPath = `logos/${req.file.filename}`;

      // Get or create company settings
      let settings = await prisma.companySettings.findFirst();

      // Delete old logo if it exists
      if (settings && settings.logoUrl) {
        deleteFile(settings.logoUrl);
      }

      if (settings) {
        // Update existing settings with new logo
        settings = await prisma.companySettings.update({
          where: { id: settings.id },
          data: {
            logoUrl: logoPath,
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new settings with logo
        settings = await prisma.companySettings.create({
          data: {
            companyName: "",
            companyEmail: "",
            logoUrl: logoPath,
          },
        });
      }

      res.json({
        message: "Logo uploaded successfully",
        logoUrl: getFileUrl(logoPath),
        settings,
      });
    } catch (error) {
      console.error("Error uploading logo:", error);

      // Clean up uploaded file if database operation failed
      if (req.file) {
        deleteFile(`logos/${req.file.filename}`);
      }

      res.status(500).json({
        error: true,
        message: "Failed to upload logo",
      });
    }
  }
);

/**
 * DELETE /api/admin/content/remove-logo
 * Remove company logo
 */
router.delete("/remove-logo", async (_req: Request, res: Response) => {
  try {
    const settings = await prisma.companySettings.findFirst();

    if (!settings || !settings.logoUrl) {
      return res.status(404).json({
        error: true,
        message: "No logo found to remove",
      });
    }

    // Delete file from filesystem
    deleteFile(settings.logoUrl);

    // Update database
    const updatedSettings = await prisma.companySettings.update({
      where: { id: settings.id },
      data: {
        logoUrl: null,
        updatedAt: new Date(),
      },
    });

    res.json({
      message: "Logo removed successfully",
      settings: updatedSettings,
    });
  } catch (error) {
    console.error("Error removing logo:", error);
    res.status(500).json({
      error: true,
      message: "Failed to remove logo",
    });
  }
});

export default router;
