import multer from "multer";
import path from "path";
import fs from "fs";
import { Request, Response, NextFunction } from "express";

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, "../../uploads");
const logosDir = path.join(uploadsDir, "logos");
const carouselDir = path.join(uploadsDir, "carousel");

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

if (!fs.existsSync(logosDir)) {
  fs.mkdirSync(logosDir, { recursive: true });
}

if (!fs.existsSync(carouselDir)) {
  fs.mkdirSync(carouselDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (
    req: Request,
    file: Express.Multer.File,
    cb: (error: Error | null, destination: string) => void
  ): void => {
    let uploadPath = uploadsDir;

    // Determine upload path based on file type
    if (file.fieldname === "logo") {
      uploadPath = logosDir;
    } else if (file.fieldname === "image") {
      uploadPath = carouselDir;
    }

    cb(null, uploadPath);
  },
  filename: (
    req: Request,
    file: Express.Multer.File,
    cb: (error: Error | null, filename: string) => void
  ): void => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    const name = file.fieldname + "-" + uniqueSuffix + ext;
    cb(null, name);
  },
});

// File filter for images only
const fileFilter = (
  req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
): void => {
  const allowedTypes = /jpeg|jpg|png|gif|svg|webp/;
  const extname = allowedTypes.test(
    path.extname(file.originalname).toLowerCase()
  );
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    cb(null, true);
  } else {
    cb(
      new Error("Only image files are allowed (jpeg, jpg, png, gif, svg, webp)")
    );
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit
  },
  fileFilter: fileFilter,
});

// Middleware for single logo upload
export const uploadLogo: (
  req: Request,
  res: Response,
  next: NextFunction
) => void = upload.single("logo");

// Middleware for multiple file uploads
export const uploadMultiple: (
  req: Request,
  res: Response,
  next: NextFunction
) => void = upload.array("files", 10);

// Error handling middleware
export const handleUploadError = (
  err: any,
  _req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (err instanceof multer.MulterError) {
    if (err.code === "LIMIT_FILE_SIZE") {
      res.status(400).json({
        error: true,
        message: "File too large. Maximum size is 2MB.",
      });
      return;
    }
    if (err.code === "LIMIT_UNEXPECTED_FILE") {
      res.status(400).json({
        error: true,
        message: "Unexpected field name.",
      });
      return;
    }
  }

  if (err) {
    res.status(400).json({
      error: true,
      message: err.message || "File upload failed.",
    });
    return;
  }

  next();
};

// Helper function to delete file
export const deleteFile = (filePath: string): boolean => {
  try {
    const fullPath = path.join(__dirname, "../../uploads", filePath);
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      return true;
    }
  } catch (error: unknown) {
    console.error("Error deleting file:", error);
  }
  return false;
};

// Helper function to get file URL
export const getFileUrl = (filePath: string | null): string | null => {
  if (!filePath) return null;

  const baseUrl = process.env.BASE_URL || "http://localhost:3001";
  return `${baseUrl}/uploads/${filePath}`;
};
